@echo off
echo Facebook Ad Library Scraper
echo -------------------------


REM %~dp0 expands to the drive and path of the currently running batch script

pushd "%~dp0"

if ERRORLEVEL 1 (
    echo CRITICAL ERROR: Could not change to script directory. Path: "%~dp0"
    if "%PUSHD_ERRORLEVEL%"=="1" ( echo pushd failed ) else ( popd )
    exit /b 99
)
echo Running from directory: %CD%

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    exit /b 1
)

REM Check if required packages are installed
echo Checking required packages...
pip show requests >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing requests package...
    pip install requests
)

pip show beautifulsoup4 >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing beautifulsoup4 package...
    pip install beautifulsoup4
)

REM Parse command line arguments
set QUERY=Educators Credit Union
set COUNTRY=US
set OUTPUT_FILE=
set START_DATE=
set DEBUG=

:parse_args
if "%~1"=="" goto run_script
if /i "%~1"=="--query" (
    set QUERY=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--country" (
    set COUNTRY=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--output" (
    set OUTPUT_FILE=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--start_date" (
    set START_DATE=%~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--debug" (
    set DEBUG=--debug
    shift
    goto parse_args
)
shift
goto parse_args

:run_script
echo Running scraper with query: "%QUERY%", country: %COUNTRY%
if not "%START_DATE%"=="" echo Start date: %START_DATE%
if "%DEBUG%"=="--debug" echo Debug mode: enabled

if "%OUTPUT_FILE%"=="" (
    if "%START_DATE%"=="" (
        python fb_ad_scraper.py --query "%QUERY%" --country %COUNTRY% %DEBUG%
    ) else (
        python fb_ad_scraper.py --query "%QUERY%" --country %COUNTRY% --start_date "%START_DATE%" %DEBUG%
    )
) else (
    echo Output will be saved to: %OUTPUT_FILE%
    if "%START_DATE%"=="" (
        python fb_ad_scraper.py --query "%QUERY%" --country %COUNTRY% --output "%OUTPUT_FILE%" %DEBUG%
    ) else (
        python fb_ad_scraper.py --query "%QUERY%" --country %COUNTRY% --start_date "%START_DATE%" --output "%OUTPUT_FILE%" %DEBUG%
    )
)

echo.
if %ERRORLEVEL% equ 0 (
    echo Scraping completed successfully.
    exit /b 0
) else (
    echo Scraping failed with error code %ERRORLEVEL%.
    exit /b %ERRORLEVEL%
)
