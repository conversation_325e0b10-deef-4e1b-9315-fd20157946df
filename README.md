# Facebook Ad Library Scraper

A Python web scraper for Facebook's Ad Library that extracts ad data without using their API.

## Features

- Scrapes Facebook Ad Library data based on a search query
- Extracts JSON data embedded in the page's HTML
- Outputs clean, formatted JSON to the terminal
- Allows customizing the search query via command-line arguments

## Requirements

- Python 3.6+
- Required packages: requests, beautifulsoup4

## Installation

1. Clone this repository:

   ```
   git clone https://github.com/yourusername/facebook_ad_scraper.git
   cd facebook_ad_scraper
   ```

2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

## Usage

### Available Parameters

| Parameter      | Description                                   | Default Value            | Required |
| -------------- | --------------------------------------------- | ------------------------ | -------- |
| `--query`      | Search query for Facebook Ad Library          | "Educators Credit Union" | No       |
| `--country`    | Country code for the ads                      | "US"                     | No       |
| `--start_date` | Filter ads by start date (format: YYYY-MM-DD) | None                     | No       |
| `--output`     | Path to save results as CSV                   | None (prints to console) | No       |
| `--debug`      | Enable verbose debug output                   | Disabled                 | No       |

### Running the Scraper

#### Using Python directly

```bash
python fb_ad_scraper.py --query "Target Corporation" --country "GB" --start_date 2023-01-01 --output "results.csv" --debug
```

#### Using Windows Batch File

```
run_scraper.bat --query "Target Corporation" --country "GB" --start_date 2023-01-01 --output "results.csv" --debug
```

#### Using Shell Script (Linux/Mac/Git Bash)

```bash
./run_scraper.sh --query "Target Corporation" --country "GB" --start_date 2023-01-01 --output "results.csv" --debug
```

All parameters are optional and can be used in any combination.

## How It Works

The scraper:

1. Builds a URL for the Facebook Ad Library with the specified search query
2. Fetches the HTML content of the page
3. Extracts JSON data embedded in script tags with specific attributes
4. Navigates through the JSON structure to find ad data
5. Outputs the formatted JSON data

## Notes

- This scraper is for educational purposes only
- Facebook may change their page structure, which could break this scraper
- Use responsibly and respect Facebook's terms of service and rate limits
- If the scraper fails to extract data, it will save the HTML content to a file named `fb_ad_library_debug.html` for debugging
- Facebook may employ anti-scraping measures, so the script includes retry logic and user-agent rotation
- For best results, avoid making too many requests in a short period of time
- SSL certificate validation is disabled to allow use with self-signed certificates in controlled environments
- **Security Warning**: Disabling SSL certificate validation can expose you to man-in-the-middle attacks. Only use this in trusted environments

## License

MIT
