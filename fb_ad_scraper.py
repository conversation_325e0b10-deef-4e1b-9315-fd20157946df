#!/usr/bin/env python3
"""
Facebook Ad Library Scraper

This script scrapes ad data from Facebook's Ad Library without using their API.
It extracts JSON data embedded in the page's HTML and outputs it as formatted JSON.
"""

import argparse
import json
import re
import sys
import time
import random
from urllib.parse import quote
import datetime
import csv

import requests
import urllib3
from bs4 import BeautifulSoup


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Scrape Facebook Ad Library data.')
    parser.add_argument(
        '--query',
        type=str,
        default="Educators Credit Union",
        help='Search query for Facebook Ad Library (default: "Educators Credit Union")'
    )
    parser.add_argument(
        '--country',
        type=str,
        default="US",
        help='Country code for the ads (default: "US")'
    )
    parser.add_argument(
        '--start_date',
        type=str,
        help='Optional start date to filter ads (format: YYYY-MM-DD)'
    )
    parser.add_argument(
    '--debug',
    action='store_true',
    help='Enable verbose debug output'
    )
    parser.add_argument(
    '--output',
    type=str,
    help='Optional CSV output file path (e.g., C:/Users/<USER>/Desktop/ads.csv)'
    )
    return parser.parse_args()


def build_url(query, country="US"):
    """Build the Facebook Ad Library URL with the given query."""
    encoded_query = quote(query)
    return (
        f"https://www.facebook.com/ads/library/"
        f"?active_status=active&ad_type=all&country={country}"
        f"&is_targeted_country=false&media_type=all&q={encoded_query}"
        f"&search_type=keyword_unordered"
    )


def fetch_page(url, max_retries=3,debug=False):
    """Fetch the HTML content of the Facebook Ad Library page."""
    # Rotate between different user agents to avoid detection
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
    ]

    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
        'Referer': 'https://www.facebook.com/',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
    }

    # Disable SSL certificate verification warnings
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    if debug:
        print("SSL certificate validation is disabled as requested.", file=sys.stderr)

    for attempt in range(max_retries):
        try:
            # Add a small random delay between requests to mimic human behavior
            if attempt > 0:
                delay = 2 + random.random() * 3  # Random delay between 2-5 seconds
                if debug:
                    print(f"Retry attempt {attempt+1}/{max_retries}. Waiting {delay:.2f} seconds...", file=sys.stderr)
                time.sleep(delay)

            # Set verify=False to disable SSL certificate validation
            response = requests.get(url, headers=headers, timeout=30, verify=False)
            response.raise_for_status()

            # Check if we got a valid response with content
            if len(response.text) < 1000:
                if debug:
                    print(f"Warning: Received suspiciously short response ({len(response.text)} bytes)", file=sys.stderr)
                if attempt < max_retries - 1:
                    continue

            return response.text

        except requests.exceptions.RequestException as e:
            if debug:
                print(f"Error fetching page (attempt {attempt+1}/{max_retries}): {e}", file=sys.stderr)
            if attempt < max_retries - 1:
                continue
            else:
                if debug:
                    print("Max retries exceeded.", file=sys.stderr)
                sys.exit(1)


def extract_json_data(html_content, debug=False):
    """Extract JSON data from script tags in the HTML."""
    soup = BeautifulSoup(html_content, 'html.parser')

    # Find all script tags with the specified attributes
    script_tags = soup.find_all(
        'script',
        attrs={
            'type': 'application/json',
            'data-sjs': '',
            'data-processed': '1'
        }
    )

    # Also look for script tags with data-content-len attribute
    script_tags.extend(
        soup.find_all(
            'script',
            attrs={
                'type': 'application/json',
                'data-content-len': True,
                'data-sjs': '',
                'data-processed': '1'
            }
        )
    )

    # Look for any script tags that might contain the ad data
    if not script_tags:
        if debug:
            print("No matching script tags with specific attributes found. Trying broader search...", file=sys.stderr)
        script_tags = soup.find_all('script', attrs={'type': 'application/json'})

        if not script_tags:
            if debug:
                print("No script tags with application/json type found.", file=sys.stderr)
            return None
        if debug:
            print(f"Found {len(script_tags)} potential script tags to analyze.", file=sys.stderr)

    # Process each script tag to find the one with ad data
    for i, script in enumerate(script_tags):
        if not script.string:
            continue

        try:
            if debug:
                print(f"Analyzing script tag {i+1}/{len(script_tags)}...", file=sys.stderr)
            data = json.loads(script.string)

            # Try to navigate to the ad data path as specified in requirements
            # require[0][3][0]["__bbox"]["require"][0][1][1]["__bbox"]["result"]["data"]["ad_library_main"]["search_results_connection"]["edges"][*]["node"]["collated_results"]
            try:
                if 'require' in data and len(data['require']) > 0:
                    if len(data['require'][0]) > 3 and '__bbox' in data['require'][0][3][0]:
                        bbox_data = data['require'][0][3][0]['__bbox']

                        if 'require' in bbox_data and len(bbox_data['require']) > 0:
                            inner_bbox = bbox_data['require'][0][1][1]['__bbox']

                            if 'result' in inner_bbox and 'data' in inner_bbox['result']:
                                ad_data = inner_bbox['result']['data']['ad_library_main']['search_results_connection']['edges']

                                # Extract collated_results from each node
                                collated_results = []
                                for edge in ad_data:
                                    if 'node' in edge and 'collated_results' in edge['node']:
                                        collated_results.append(edge['node']['collated_results'])

                                if collated_results:
                                    if debug:
                                        print(f"Found ad data in script tag {i+1}!", file=sys.stderr)
                                    return collated_results
            except (KeyError, IndexError, TypeError) as e:
                # If the expected path doesn't work, try a more flexible approach
                pass

            # If the specific path doesn't work, try to search for key structures
            # that might indicate ad data
            if isinstance(data, dict):
                # Look for ad_library_main in any nested structure
                def search_for_ad_data(obj, path=""):
                    if isinstance(obj, dict):
                        if "ad_library_main" in obj:
                            if debug:
                                print(f"Found ad_library_main at path: {path}.ad_library_main", file=sys.stderr)
                            try:
                                edges = obj["ad_library_main"]["search_results_connection"]["edges"]
                                collated_results = []
                                for edge in edges:
                                    if "node" in edge and "collated_results" in edge["node"]:
                                        collated_results.append(edge["node"]["collated_results"])
                                if collated_results:
                                    return collated_results
                            except (KeyError, TypeError):
                                pass

                        for key, value in obj.items():
                            result = search_for_ad_data(value, f"{path}.{key}" if path else key)
                            if result:
                                return result
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            result = search_for_ad_data(item, f"{path}[{i}]")
                            if result:
                                return result
                    return None

                result = search_for_ad_data(data)
                if result:
                    if debug:
                        print(f"Found ad data using flexible search in script tag {i+1}!", file=sys.stderr)
                    return result

        except json.JSONDecodeError:
            # Skip invalid JSON
            continue
    if debug:
        print("Could not find ad data in any of the script tags.", file=sys.stderr)

    # As a last resort, try to find any JSON-like structures in the HTML
    if debug:
        print("Attempting to find JSON-like structures in the HTML...", file=sys.stderr)
    json_pattern = re.compile(r'(\{.*"ad_library_main".*\})')
    matches = json_pattern.findall(html_content)

    for match in matches:
        try:
            data = json.loads(match)
            # Try to extract ad data from this JSON
            # (similar logic as above)
            if debug:
                print("Found potential JSON structure with ad_library_main. Attempting to extract data...", file=sys.stderr)
            # Implementation would be similar to the above search_for_ad_data function
        except json.JSONDecodeError:
            continue

    return None


'''def save_html_for_debugging(html_content, filename="fb_ad_library_debug.html",debug=False):
    """Save the HTML content to a file for debugging purposes."""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        if debug:
            print(f"Saved HTML content to {filename} for debugging.", file=sys.stderr)
    except Exception as e:
        if debug:
                     print(f"Error saving HTML content: {e}", file=sys.stderr)
'''

def load_excluded_pages(filepath="exclude_pages.txt",debug=False):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return set(line.strip() for line in f if line.strip())
    except FileNotFoundError:
        if debug:
            print(f"Warning: Exclusion file '{filepath}' not found.", file=sys.stderr)
        return set()

def main():
    

    """Main function to run the scraper."""
    args = parse_arguments()
    url = build_url(args.query, args.country)

    if args.debug:
        print(f"Scraping Facebook Ad Library for query: '{args.query}'", file=sys.stderr)
        print(f"URL: {url}", file=sys.stderr)
        print(f"Using country code: {args.country}", file=sys.stderr)

    html_content = fetch_page(url)

    # Check if we got a valid response
    if not html_content or len(html_content) < 1000:
        if args.debug:
            print("Warning: Received suspiciously short HTML content.", file=sys.stderr)
            print("This might indicate that Facebook is blocking the request.", file=sys.stderr)
        #     print("Saving HTML content for debugging...", file=sys.stderr)
        # save_html_for_debugging(html_content)
        sys.exit(1)

    # Extract ad data from the HTML
    ad_data = extract_json_data(html_content)

    if not ad_data:
        if args.debug:
            print("No ad data found.", file=sys.stderr)
        # save_html_for_debugging(html_content)
        sys.exit(1)

    base_url = "https://www.facebook.com/ads/library/?id="
    # Flatten the ad data
    flat_ads = []
    for group in ad_data:
        if isinstance(group, list):
            flat_ads.extend([ad for ad in group if isinstance(ad, dict)])

    if not flat_ads:
        if args.debug:
            print("No ads found after flattening.", file=sys.stderr)
        sys.exit(1)

    # Optional: filter by start date
    if hasattr(args, 'start_date') and args.start_date:
        try:
            start_dt = datetime.datetime.strptime(args.start_date, "%Y-%m-%d").replace(tzinfo=datetime.UTC)
            flat_ads = [
                ad for ad in flat_ads
                if 'start_date' in ad and isinstance(ad['start_date'], int) and
                datetime.datetime.fromtimestamp(ad['start_date'], datetime.UTC) >= start_dt
            ]
            if args.debug:
                print(f"Filtered ads to those starting on or after {args.start_date}.", file=sys.stderr)
        except ValueError:
            if args.debug:
                print("Invalid start date format. Use YYYY-MM-DD.", file=sys.stderr)
            sys.exit(1)
    # for group in ad_data:
    #     for ad in group:
    excluded_pages = load_excluded_pages()
    flat_ads = [
        ad for ad in flat_ads
        if ad.get("page_name") not in excluded_pages
    ]
    ad_links = []
    for ad in flat_ads:
        ad_id = ad.get("ad_archive_id")
        if ad_id:
                ad_links.append(f"https://www.facebook.com/ads/library/?id={ad_id}")
                if args.debug:
                    print(ad_links[-1])
                    #print(f"{base_url}{ad_id}")
    
        # start date as param
        # white list that we don't look for "page_name"
    print(f"Successfully extracted data for {len(ad_links)} ads.", file=sys.stderr)
    # If output file path is provided, write to CSV
    if args.output:
        try:
            with open(args.output, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow([f"{len(ad_links)} ad links"])
                for link in ad_links:
                    writer.writerow([link])
                print(f"Wrote {len(ad_links)} ad links to {args.output}", file=sys.stderr)
        except Exception as e:
            if args.debug:
                print(f"Failed to write CSV: {e}", file=sys.stderr)
            sys.exit(1)

if __name__ == "__main__":
    main()
