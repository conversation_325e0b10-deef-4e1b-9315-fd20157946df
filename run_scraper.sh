#!/bin/bash

echo "Facebook Ad Library Scraper"
echo "-------------------------"

# Check if Python is installed
if ! command -v python &> /dev/null; then
    echo "Error: Python is not installed or not in PATH."
    echo "Please install Python from https://www.python.org/downloads/"
    exit 1
fi

# Check if required packages are installed
echo "Checking required packages..."
if ! python -c "import requests" &> /dev/null; then
    echo "Installing requests package..."
    pip install requests
fi

if ! python -c "import bs4" &> /dev/null; then
    echo "Installing beautifulsoup4 package..."
    pip install beautifulsoup4
fi

# Default values
QUERY="Educators Credit Union"
COUNTRY="US"
OUTPUT_FILE=""
START_DATE=""
DEBUG=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --query)
            QUERY="$2"
            shift 2
            ;;
        --country)
            COUNTRY="$2"
            shift 2
            ;;
        --output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --start_date)
            START_DATE="$2"
            shift 2
            ;;
        --debug)
            DEBUG="--debug"
            shift
            ;;
        *)
            shift
            ;;
    esac
done

echo "Running scraper with query: \"$QUERY\", country: $COUNTRY"
if [ -n "$START_DATE" ]; then
    echo "Start date: $START_DATE"
fi
if [ "$DEBUG" = "--debug" ]; then
    echo "Debug mode: enabled"
fi

if [ -z "$OUTPUT_FILE" ]; then
    if [ -z "$START_DATE" ]; then
        python fb_ad_scraper.py --query "$QUERY" --country "$COUNTRY" $DEBUG
    else
        python fb_ad_scraper.py --query "$QUERY" --country "$COUNTRY" --start_date "$START_DATE" $DEBUG
    fi
else
    echo "Output will be saved to: $OUTPUT_FILE"
    if [ -z "$START_DATE" ]; then
        python fb_ad_scraper.py --query "$QUERY" --country "$COUNTRY" --output "$OUTPUT_FILE" $DEBUG
    else
        python fb_ad_scraper.py --query "$QUERY" --country "$COUNTRY" --start_date "$START_DATE" --output "$OUTPUT_FILE" $DEBUG
    fi
fi

echo ""
if [ $? -eq 0 ]; then
    echo "Scraping completed successfully."
else
    echo "Scraping failed with error code $?."
fi

read -p "Press Enter to continue..."
